# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للبرنامج
Main Application Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QMenuBar, QMenu, QToolBar, QStatusBar,
                               QLabel, QPushButton, QFrame, QGridLayout, QMessageBox,
                               QScrollArea, QGraphicsDropShadowEffect, QSizePolicy)
from PySide6.QtCore import Qt, QSize, Signal, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QIcon, QPixmap, QFont, QAction, QLinearGradient, QColor, QPainter, QPalette

from ..utils.arabic_support import reshape_arabic_text
from .settings.settings_window import SettingsWindow
from .items.items_window import ItemsWindow
from .suppliers.suppliers_window import SuppliersWindow
from .shipments.shipments_window import ShipmentsWindow
from .styles.style_manager import style_manager

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام إدارة الشحنات المتكامل - ProShipment")
        self.setMinimumSize(1200, 800)

        # تعيين النافذة لتفتح في وضع ملء الشاشة
        self.showMaximized()

        # تحميل الثيم الحديث
        style_manager.load_theme("modern")

        # إعداد الستايل العام
        self.setup_global_style()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()

        # إعداد الرسوم المتحركة
        self.setup_animations()

        # متغيرات النوافذ المنفصلة
        self.settings_window = None
        self.items_window = None
        self.suppliers_window = None
        self.shipments_window = None

    def setup_global_style(self):
        """إعداد الستايل العام المتقدم للتطبيق"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.3 #764ba2, stop:0.7 #f093fb, stop:1 #f5576c);
                background-attachment: fixed;
            }

            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(44, 62, 80, 0.95), stop:0.5 rgba(52, 73, 94, 0.95), stop:1 rgba(44, 62, 80, 0.95));
                backdrop-filter: blur(10px);
                color: white;
                border: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                padding: 8px;
                font-size: 13px;
                font-weight: 600;
            }

            QMenuBar::item {
                background: transparent;
                padding: 10px 18px;
                border-radius: 8px;
                margin: 2px 4px;
                transition: all 0.3s ease;
            }

            QMenuBar::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.25), stop:1 rgba(255, 255, 255, 0.15));
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            }

            QMenu {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(15px);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                padding: 8px;
                font-size: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            }

            QMenu::item {
                padding: 12px 24px;
                border-radius: 8px;
                margin: 2px;
                color: #2c3e50;
                transition: all 0.2s ease;
            }

            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                transform: translateY(-1px);
            }

            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(52, 73, 94, 0.9), stop:0.5 rgba(44, 62, 80, 0.9), stop:1 rgba(52, 73, 94, 0.9));
                backdrop-filter: blur(10px);
                border: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                spacing: 8px;
                padding: 10px;
            }

            QToolBar QToolButton {
                background: transparent;
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.2);
                padding: 10px 16px;
                border-radius: 8px;
                font-size: 12px;
                font-weight: 600;
                transition: all 0.3s ease;
            }

            QToolBar QToolButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.25), stop:1 rgba(255, 255, 255, 0.15));
                border-color: rgba(255, 255, 255, 0.4);
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }

            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(236, 240, 241, 0.95), stop:1 rgba(189, 195, 199, 0.95));
                backdrop-filter: blur(10px);
                border-top: 1px solid rgba(149, 165, 166, 0.3);
                color: #2c3e50;
                font-size: 12px;
                font-weight: 500;
            }
        """)

    def setup_animations(self):
        """إعداد الرسوم المتحركة"""
        # مؤقت لتحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)  # تحديث كل ثانية

    def setup_ui(self):
        """إعداد واجهة المستخدم"""

        # الويدجت المركزي مع منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 14px;
                border-radius: 7px;
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(102, 126, 234, 0.8), stop:1 rgba(118, 75, 162, 0.8));
                border-radius: 7px;
                min-height: 30px;
                margin: 2px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """)

        central_widget = QWidget()
        scroll_area.setWidget(central_widget)
        self.setCentralWidget(scroll_area)

        # التخطيط الرئيسي الأفقي
        main_horizontal_layout = QHBoxLayout(central_widget)
        main_horizontal_layout.setSpacing(20)
        main_horizontal_layout.setContentsMargins(20, 20, 20, 20)

        # الشريط الجانبي للوصول السريع
        self.create_sidebar(main_horizontal_layout)

        # المحتوى الرئيسي
        main_content_widget = QWidget()
        main_content_layout = QVBoxLayout(main_content_widget)
        main_content_layout.setSpacing(25)
        main_content_layout.setContentsMargins(10, 10, 10, 10)

        # إضافة الهيدر المحسن
        self.create_enhanced_header(main_content_layout)

        # إضافة قسم الإحصائيات السريعة
        self.create_stats_section(main_content_layout)

        # شبكة الأزرار الرئيسية المحسنة
        self.create_main_buttons_section(main_content_layout)

        # إضافة قسم المعلومات السريعة
        self.create_quick_info_section(main_content_layout)

        main_content_layout.addStretch()
        main_horizontal_layout.addWidget(main_content_widget, 4)

    def create_sidebar(self, layout):
        """إنشاء الشريط الجانبي للوصول السريع"""
        sidebar_frame = QFrame()
        sidebar_frame.setFixedWidth(280)
        sidebar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(52, 73, 94, 0.95),
                    stop:0.5 rgba(44, 62, 80, 0.95),
                    stop:1 rgba(52, 73, 94, 0.95));
                backdrop-filter: blur(15px);
                border-radius: 25px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            }
        """)

        sidebar_layout = QVBoxLayout(sidebar_frame)
        sidebar_layout.setSpacing(20)
        sidebar_layout.setContentsMargins(20, 25, 20, 25)

        # شعار وعنوان الشريط الجانبي
        logo_section = QFrame()
        logo_layout = QVBoxLayout(logo_section)
        logo_layout.setSpacing(10)
        logo_layout.setAlignment(Qt.AlignCenter)

        # الشعار
        logo_label = QLabel("🚢")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_font = QFont()
        logo_font.setPointSize(32)
        logo_label.setFont(logo_font)
        logo_label.setStyleSheet("color: white; background: transparent;")

        # اسم التطبيق
        app_name = QLabel("ProShipment")
        app_name.setAlignment(Qt.AlignCenter)
        name_font = QFont()
        name_font.setPointSize(18)
        name_font.setBold(True)
        name_font.setFamily("Arial")
        app_name.setFont(name_font)
        app_name.setStyleSheet("""
            color: white;
            background: transparent;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: 1px;
        """)

        # نسخة التطبيق
        version_label = QLabel("الإصدار 2.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_font = QFont()
        version_font.setPointSize(11)
        version_label.setFont(version_font)
        version_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.7);
            background: transparent;
        """)

        logo_layout.addWidget(logo_label)
        logo_layout.addWidget(app_name)
        logo_layout.addWidget(version_label)
        sidebar_layout.addWidget(logo_section)

        # خط فاصل
        separator = QFrame()
        separator.setFixedHeight(2)
        separator.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 transparent, stop:0.5 rgba(255, 255, 255, 0.3), stop:1 transparent);
            border: none;
            margin: 10px 20px;
        """)
        sidebar_layout.addWidget(separator)

        # قائمة الوصول السريع
        quick_access_items = [
            ("📊", "لوحة التحكم", self.show_dashboard),
            ("📦", "الشحنات", self.open_shipments),
            ("👥", "العملاء", self.open_customers),
            ("🏭", "الموردين", self.open_suppliers),
            ("📋", "التقارير", self.open_reports),
            ("⚙️", "الإعدادات", self.open_settings),
            ("❓", "المساعدة", self.show_help),
            ("🚪", "تسجيل الخروج", self.logout)
        ]

        for icon, text, callback in quick_access_items:
            sidebar_item = self.create_sidebar_item(icon, text, callback)
            sidebar_layout.addWidget(sidebar_item)

        sidebar_layout.addStretch()

        # معلومات المستخدم في الأسفل
        user_info = self.create_user_info_section()
        sidebar_layout.addWidget(user_info)

        layout.addWidget(sidebar_frame, 1)

    def create_sidebar_item(self, icon, text, callback):
        """إنشاء عنصر في الشريط الجانبي"""
        item_frame = QFrame()
        item_frame.setCursor(Qt.PointingHandCursor)
        item_frame.setStyleSheet("""
            QFrame {
                background: transparent;
                border-radius: 12px;
                padding: 12px;
                margin: 2px;
            }
            QFrame:hover {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)

        item_layout = QHBoxLayout(item_frame)
        item_layout.setSpacing(15)
        item_layout.setContentsMargins(15, 10, 15, 10)

        # الأيقونة
        icon_label = QLabel(icon)
        icon_font = QFont()
        icon_font.setPointSize(16)
        icon_label.setFont(icon_font)
        icon_label.setStyleSheet("color: white; background: transparent;")
        icon_label.setMinimumWidth(25)

        # النص
        text_label = QLabel(text)
        text_font = QFont()
        text_font.setPointSize(13)
        text_font.setWeight(QFont.Medium)
        text_label.setFont(text_font)
        text_label.setStyleSheet("""
            color: white;
            background: transparent;
            letter-spacing: 0.5px;
        """)

        item_layout.addWidget(icon_label)
        item_layout.addWidget(text_label)
        item_layout.addStretch()

        # ربط النقر بالوظيفة
        def on_click():
            if callback:
                callback()

        item_frame.mousePressEvent = lambda event: on_click()

        return item_frame

    def create_user_info_section(self):
        """إنشاء قسم معلومات المستخدم"""
        user_frame = QFrame()
        user_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                padding: 15px;
            }
        """)

        user_layout = QVBoxLayout(user_frame)
        user_layout.setSpacing(8)
        user_layout.setContentsMargins(15, 12, 15, 12)

        # اسم المستخدم
        username_label = QLabel("👤 المدير العام")
        username_font = QFont()
        username_font.setPointSize(12)
        username_font.setBold(True)
        username_label.setFont(username_font)
        username_label.setStyleSheet("color: white; background: transparent;")

        # حالة الاتصال
        status_label = QLabel("🟢 متصل")
        status_font = QFont()
        status_font.setPointSize(10)
        status_label.setFont(status_font)
        status_label.setStyleSheet("color: rgba(255, 255, 255, 0.8); background: transparent;")

        user_layout.addWidget(username_label)
        user_layout.addWidget(status_label)

        return user_frame

    def create_enhanced_header(self, layout):
        """إنشاء الهيدر الرئيسي المتقدم"""
        header_frame = QFrame()
        header_frame.setMinimumHeight(180)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(102, 126, 234, 0.95),
                    stop:0.3 rgba(118, 75, 162, 0.95),
                    stop:0.7 rgba(240, 147, 251, 0.95),
                    stop:1 rgba(245, 87, 108, 0.95));
                border-radius: 25px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                backdrop-filter: blur(20px);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(15)
        header_layout.setContentsMargins(40, 30, 40, 30)

        # العنوان الرئيسي المطور
        title_label = QLabel("🚢 ProShipment Pro • الجيل الثاني")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(28)
        title_font.setBold(True)
        title_font.setFamily("Arial")
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                background: transparent;
                padding: 20px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
                letter-spacing: 2px;
            }
        """)
        header_layout.addWidget(title_label)

        # العنوان الفرعي المحسن
        subtitle_label = QLabel("نظام إدارة الشحنات والتوريد المتطور • واجهة عصرية • أداء استثنائي")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_font = QFont()
        subtitle_font.setPointSize(14)
        subtitle_font.setWeight(QFont.Medium)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.95);
                background: transparent;
                padding: 12px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
                letter-spacing: 0.8px;
            }
        """)
        header_layout.addWidget(subtitle_label)

        # إضافة معلومات إضافية
        info_label = QLabel("✨ تصميم متطور • تفاعل سلس • إدارة ذكية")
        info_label.setAlignment(Qt.AlignCenter)
        info_font = QFont()
        info_font.setPointSize(11)
        info_label.setFont(info_font)
        info_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                background: transparent;
                padding: 8px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            }
        """)
        header_layout.addWidget(info_label)

        # إضافة خط فاصل مع تأثير متدرج
        separator = QFrame()
        separator.setFixedHeight(2)
        separator.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:0.5 rgba(255, 255, 255, 0.6), stop:1 transparent);
                border: none;
                margin: 10px 50px;
            }
        """)
        header_layout.addWidget(separator)

        layout.addWidget(header_frame)

    def create_stats_section(self, layout):
        """إنشاء قسم الإحصائيات السريعة المتقدم"""
        stats_frame = QFrame()
        stats_frame.setMinimumHeight(140)
        stats_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
        """)

        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setSpacing(25)
        stats_layout.setContentsMargins(30, 25, 30, 25)

        # بيانات الإحصائيات المحدثة
        stats_data = [
            ("📦", "إجمالي الشحنات", "1,234", "#667eea", "#764ba2"),
            ("🏭", "الموردين النشطين", "89", "#f093fb", "#f5576c"),
            ("📋", "الأصناف المسجلة", "5,678", "#4facfe", "#00f2fe"),
            ("⏱️", "الشحنات المعلقة", "23", "#43e97b", "#38f9d7")
        ]

        for icon, title, value, color1, color2 in stats_data:
            stat_widget = self.create_stat_widget_advanced(icon, title, value, color1, color2)
            stats_layout.addWidget(stat_widget)

        layout.addWidget(stats_frame)

    def create_stat_widget_advanced(self, icon, title, value, color1, color2):
        """إنشاء ويدجت إحصائية متقدمة"""
        widget = QFrame()
        widget.setMinimumHeight(100)
        widget.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                border-radius: 16px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            }}
            QFrame:hover {{
                transform: translateY(-2px);
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            }}
        """)

        layout = QVBoxLayout(widget)
        layout.setSpacing(10)
        layout.setAlignment(Qt.AlignCenter)
        layout.setContentsMargins(20, 15, 20, 15)

        # الأيقونة مع تأثيرات
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_font = QFont()
        icon_font.setPointSize(28)
        icon_label.setFont(icon_font)
        icon_label.setStyleSheet("""
            color: white;
            background: transparent;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        """)
        layout.addWidget(icon_label)

        # القيمة مع تأثيرات متقدمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_font = QFont()
        value_font.setPointSize(24)
        value_font.setBold(True)
        value_font.setFamily("Arial")
        value_label.setFont(value_font)
        value_label.setStyleSheet("""
            color: white;
            background: transparent;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
            font-weight: 700;
        """)
        layout.addWidget(value_label)

        # العنوان مع تأثيرات
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        title_font = QFont()
        title_font.setPointSize(11)
        title_font.setWeight(QFont.Medium)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.95);
            background: transparent;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            letter-spacing: 0.5px;
        """)
        layout.addWidget(title_label)

        return widget

    def darken_color(self, color):
        """تغميق اللون للتأثير عند التمرير"""
        color_map = {
            "#3498db": "#2980b9",
            "#2ecc71": "#27ae60",
            "#e74c3c": "#c0392b",
            "#f39c12": "#e67e22"
        }
        return color_map.get(color, color)

    def create_main_buttons_section(self, layout):
        """إنشاء قسم الأزرار الرئيسية المتقدم"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
        """)

        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setSpacing(25)
        buttons_layout.setContentsMargins(30, 25, 30, 25)

        # عنوان القسم مع تأثيرات متقدمة
        section_title = QLabel("🎛️ الأنظمة الرئيسية")
        section_title.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(22)
        title_font.setBold(True)
        title_font.setFamily("Arial")
        section_title.setFont(title_font)
        section_title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: transparent;
                padding: 15px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
                letter-spacing: 1px;
            }
        """)
        buttons_layout.addWidget(section_title)

        # خط فاصل متدرج
        separator = QFrame()
        separator.setFixedHeight(2)
        separator.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:0.5 rgba(44, 62, 80, 0.3), stop:1 transparent);
                border: none;
                margin: 5px 40px;
            }
        """)
        buttons_layout.addWidget(separator)

        # شبكة الأزرار مع مسافات محسنة
        grid_layout = QGridLayout()
        grid_layout.setSpacing(30)
        grid_layout.setContentsMargins(15, 15, 15, 15)

        # إنشاء أزرار الأنظمة الرئيسية
        self.create_system_buttons(grid_layout)

        buttons_layout.addLayout(grid_layout)
        layout.addWidget(buttons_frame)

    def create_quick_info_section(self, layout):
        """إنشاء قسم المعلومات السريعة المحسن"""
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 25px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.12);
            }
        """)

        info_layout = QVBoxLayout(info_frame)
        info_layout.setSpacing(25)
        info_layout.setContentsMargins(35, 30, 35, 30)

        # عنوان القسم مع تصميم متطور
        section_title = QLabel("⚡ لوحة المعلومات السريعة")
        section_title.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(22)
        title_font.setBold(True)
        title_font.setFamily("Arial")
        section_title.setFont(title_font)
        section_title.setStyleSheet("""
            QLabel {
                color: #1a202c;
                background: transparent;
                padding: 20px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
                letter-spacing: 1px;
            }
        """)
        info_layout.addWidget(section_title)

        # خط فاصل متدرج محسن
        separator = QFrame()
        separator.setFixedHeight(3)
        separator.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent,
                    stop:0.2 rgba(102, 126, 234, 0.4),
                    stop:0.5 rgba(118, 75, 162, 0.6),
                    stop:0.8 rgba(240, 147, 251, 0.4),
                    stop:1 transparent);
                border: none;
                border-radius: 2px;
                margin: 10px 40px;
            }
        """)
        info_layout.addWidget(separator)

        # شبكة المعلومات السريعة
        info_grid_layout = QGridLayout()
        info_grid_layout.setSpacing(20)
        info_grid_layout.setContentsMargins(10, 10, 10, 10)

        # بيانات المعلومات السريعة
        quick_info_data = [
            ("🏢", "معلومات الشركة", "عرض تفاصيل الشركة والفروع", "#667eea", "#764ba2", 0, 0),
            ("📊", "التقارير السريعة", "الوصول السريع للتقارير المهمة", "#f093fb", "#f5576c", 0, 1),
            ("⚙️", "الإعدادات السريعة", "تخصيص إعدادات النظام", "#43e97b", "#38f9d7", 1, 0),
            ("🔔", "الإشعارات", "عرض الإشعارات والتنبيهات", "#4facfe", "#00f2fe", 1, 1),
            ("📈", "الإحصائيات المتقدمة", "تحليلات مفصلة للأداء", "#ffecd2", "#fcb69f", 2, 0),
            ("🎯", "المهام السريعة", "الوصول للمهام الأكثر استخداماً", "#a8edea", "#fed6e3", 2, 1)
        ]

        for icon, title, description, color1, color2, row, col in quick_info_data:
            info_card = self.create_quick_info_card(icon, title, description, color1, color2)
            info_grid_layout.addWidget(info_card, row, col)

        info_layout.addLayout(info_grid_layout)
        layout.addWidget(info_frame)

    def create_quick_info_card(self, icon, title, description, color1, color2):
        """إنشاء بطاقة معلومات سريعة"""
        card_frame = QFrame()
        card_frame.setMinimumSize(280, 120)
        card_frame.setCursor(Qt.PointingHandCursor)
        card_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                border-radius: 18px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color2}, stop:1 {color1});
                transform: translateY(-3px);
                box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
                border-color: rgba(255, 255, 255, 0.4);
            }}
        """)

        card_layout = QVBoxLayout(card_frame)
        card_layout.setSpacing(12)
        card_layout.setContentsMargins(20, 15, 20, 15)
        card_layout.setAlignment(Qt.AlignCenter)

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_font = QFont()
        icon_font.setPointSize(24)
        icon_label.setFont(icon_font)
        icon_label.setStyleSheet("""
            background: transparent;
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        """)

        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        title_font = QFont()
        title_font.setPointSize(13)
        title_font.setBold(True)
        title_font.setFamily("Arial")
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            background: transparent;
            color: white;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            letter-spacing: 0.5px;
        """)

        # الوصف
        desc_label = QLabel(description)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        desc_font = QFont()
        desc_font.setPointSize(10)
        desc_font.setWeight(QFont.Medium)
        desc_label.setFont(desc_font)
        desc_label.setStyleSheet("""
            background: transparent;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        """)

        card_layout.addWidget(icon_label)
        card_layout.addWidget(title_label)
        card_layout.addWidget(desc_label)

        return card_frame

    # دوال الشريط الجانبي للوصول السريع
    def show_dashboard(self):
        """عرض لوحة التحكم الرئيسية"""
        print("عرض لوحة التحكم الرئيسية")

    def open_customers(self):
        """فتح نافذة إدارة العملاء"""
        print("فتح نافذة إدارة العملاء")

    def open_reports(self):
        """فتح نافذة التقارير"""
        print("فتح نافذة التقارير")

    def show_help(self):
        """عرض نافذة المساعدة والدعم"""
        print("عرض نافذة المساعدة والدعم")

    def logout(self):
        """تسجيل الخروج من النظام"""
        reply = QMessageBox.question(self, 'تأكيد الخروج',
                                   'هل تريد تسجيل الخروج من النظام؟',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.close()

    def create_system_buttons(self, layout):
        """إنشاء أزرار الأنظمة الرئيسية"""

        # بيانات الأزرار مع الألوان المتدرجة المتقدمة
        buttons_data = [
            ("⚙️", "إعدادات", "الإعدادات العامة للنظام", "إدارة إعدادات النظام والشركة والمستخدمين", self.open_settings, 0, 0, "#667eea", "#764ba2"),
            ("📦", "أصناف", "إدارة الأصناف والمخزون", "إدارة الأصناف ووحدات القياس والمجموعات", self.open_items, 0, 1, "#4facfe", "#00f2fe"),
            ("🏭", "موردين", "إدارة الموردين والعملاء", "إدارة بيانات الموردين والعمليات والتقارير", self.open_suppliers, 0, 2, "#43e97b", "#38f9d7"),
            ("🚢", "شحنات", "إدارة الشحنات والتتبع", "نظام إدارة وتتبع الشحنات المتكامل", self.open_shipments, 1, 0, "#f093fb", "#f5576c"),
            ("🏛️", "جمارك", "الإدخالات الجمركية", "نظام الإدخالات الجمركية (قيد التطوير)", self.show_under_development, 1, 1, "#ffecd2", "#fcb69f"),
            ("💰", "تكاليف", "إدارة التكاليف والمصروفات", "نظام إدارة التكاليف (قيد التطوير)", self.show_under_development, 1, 2, "#a8edea", "#fed6e3"),
        ]

        for icon, title, subtitle, tooltip, callback, row, col, color1, color2 in buttons_data:
            button = self.create_main_button_advanced(icon, title, subtitle, tooltip, callback, color1, color2)
            layout.addWidget(button, row, col)
    
    def create_main_button_advanced(self, icon, title, subtitle, tooltip, callback, color1, color2):
        """إنشاء زر رئيسي متقدم"""
        button = QPushButton()
        button.setToolTip(tooltip)
        button.setMinimumSize(350, 160)
        button.clicked.connect(callback)
        button.setCursor(Qt.PointingHandCursor)

        # تطبيق الستايل المتقدم
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 20px;
                color: white;
                font-weight: bold;
                text-align: center;
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            }}
            QPushButton:hover {{
                transform: translateY(-3px);
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
                border-color: rgba(255, 255, 255, 0.4);
            }}
            QPushButton:pressed {{
                transform: translateY(-1px);
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            }}
        """)

        # إنشاء التخطيط الداخلي للزر
        button_layout = QVBoxLayout(button)
        button_layout.setSpacing(15)
        button_layout.setAlignment(Qt.AlignCenter)
        button_layout.setContentsMargins(25, 20, 25, 20)

        # الأيقونة مع تأثيرات متقدمة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_font = QFont()
        icon_font.setPointSize(32)
        icon_label.setFont(icon_font)
        icon_label.setStyleSheet("""
            background: transparent;
            color: white;
            padding: 8px;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
        """)

        # العنوان الرئيسي
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_font.setFamily("Arial")
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            background: transparent;
            color: white;
            padding: 5px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
        """)

        # العنوان الفرعي
        subtitle_label = QLabel(subtitle)
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setWordWrap(True)
        subtitle_font = QFont()
        subtitle_font.setPointSize(11)
        subtitle_font.setWeight(QFont.Medium)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setStyleSheet("""
            background: transparent;
            color: rgba(255, 255, 255, 0.9);
            padding: 3px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        """)

        # إضافة العناصر للتخطيط
        button_layout.addWidget(icon_label)
        button_layout.addWidget(title_label)
        button_layout.addWidget(subtitle_label)

        return button
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        
        # إجراءات قائمة ملف
        new_action = QAction("جديد", self)
        new_action.setShortcut("Ctrl+N")
        file_menu.addAction(new_action)
        
        open_action = QAction("فتح", self)
        open_action.setShortcut("Ctrl+O")
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        backup_action = QAction("نسخة احتياطية", self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة الأنظمة
        systems_menu = menubar.addMenu("الأنظمة")
        
        settings_action = QAction("الإعدادات العامة", self)
        settings_action.triggered.connect(self.open_settings)
        systems_menu.addAction(settings_action)
        
        items_action = QAction("إدارة الأصناف", self)
        items_action.triggered.connect(self.open_items)
        systems_menu.addAction(items_action)
        
        suppliers_action = QAction("إدارة الموردين", self)
        suppliers_action.triggered.connect(self.open_suppliers)
        systems_menu.addAction(suppliers_action)

        shipments_action = QAction("إدارة الشحنات", self)
        shipments_action.triggered.connect(self.open_shipments)
        systems_menu.addAction(shipments_action)
        
        # قائمة العرض والثيمات
        view_menu = menubar.addMenu("العرض")

        # قائمة فرعية للثيمات
        themes_menu = view_menu.addMenu("الثيمات")

        modern_theme_action = QAction("الثيم الحديث", self)
        modern_theme_action.triggered.connect(lambda: style_manager.load_theme("modern"))
        themes_menu.addAction(modern_theme_action)

        dark_theme_action = QAction("الثيم المظلم", self)
        dark_theme_action.triggered.connect(style_manager.apply_dark_theme)
        themes_menu.addAction(dark_theme_action)

        light_theme_action = QAction("الثيم الفاتح", self)
        light_theme_action.triggered.connect(style_manager.apply_light_theme)
        themes_menu.addAction(light_theme_action)

        themes_menu.addSeparator()

        reset_theme_action = QAction("إعادة تعيين الثيم", self)
        reset_theme_action.triggered.connect(style_manager.reset_style)
        themes_menu.addAction(reset_theme_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")

        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # أزرار شريط الأدوات
        settings_action = QAction("الإعدادات", self)
        settings_action.triggered.connect(self.open_settings)
        toolbar.addAction(settings_action)
        
        items_action = QAction("الأصناف", self)
        items_action.triggered.connect(self.open_items)
        toolbar.addAction(items_action)
        
        suppliers_action = QAction("الموردين", self)
        suppliers_action.triggered.connect(self.open_suppliers)
        toolbar.addAction(suppliers_action)

        shipments_action = QAction("الشحنات", self)
        shipments_action.triggered.connect(self.open_shipments)
        toolbar.addAction(shipments_action)
        
        toolbar.addSeparator()
        
        backup_action = QAction("نسخة احتياطية", self)
        backup_action.triggered.connect(self.create_backup)
        toolbar.addAction(backup_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة المحسن"""
        status_bar = self.statusBar()

        # معلومات الحالة مع أيقونة
        self.status_label = QLabel("🟢 النظام جاهز")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #27ae60;
                font-weight: bold;
                padding: 5px 10px;
            }
        """)
        status_bar.addWidget(self.status_label)

        # معلومات المستخدم مع أيقونة
        user_label = QLabel("👤 المستخدم: مدير النظام")
        user_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                padding: 5px 10px;
            }
        """)
        status_bar.addPermanentWidget(user_label)

        # التاريخ والوقت مع أيقونة
        from datetime import datetime
        self.date_label = QLabel(f"📅 {datetime.now().strftime('%Y/%m/%d - %H:%M:%S')}")
        self.date_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                padding: 5px 10px;
            }
        """)
        status_bar.addPermanentWidget(self.date_label)

    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        from datetime import datetime
        current_time = datetime.now().strftime('%Y/%m/%d - %H:%M:%S')
        self.date_label.setText(f"📅 {current_time}")
    
    def open_settings(self):
        """فتح نافذة الإعدادات"""
        if self.settings_window is None or not self.settings_window.isVisible():
            self.settings_window = SettingsWindow(self)
        
        self.settings_window.show()
        self.settings_window.raise_()
        self.settings_window.activateWindow()
    
    def open_items(self):
        """فتح نافذة إدارة الأصناف"""
        if self.items_window is None or not self.items_window.isVisible():
            self.items_window = ItemsWindow(self)
        
        self.items_window.show()
        self.items_window.raise_()
        self.items_window.activateWindow()
    
    def open_suppliers(self):
        """فتح نافذة إدارة الموردين"""
        if self.suppliers_window is None or not self.suppliers_window.isVisible():
            self.suppliers_window = SuppliersWindow(self)
        
        self.suppliers_window.show()
        self.suppliers_window.raise_()
        self.suppliers_window.activateWindow()

    def open_shipments(self):
        """فتح نافذة إدارة الشحنات"""
        if self.shipments_window is None or not self.shipments_window.isVisible():
            self.shipments_window = ShipmentsWindow(self)

        self.shipments_window.show()
        self.shipments_window.raise_()
        self.shipments_window.activateWindow()
    
    def show_under_development(self):
        """عرض رسالة قيد التطوير"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "هذه الميزة قيد التطوير وستكون متاحة في الإصدارات القادمة."
        )
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        from ..database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        if db_manager.backup_database():
            QMessageBox.information(
                self,
                "نسخة احتياطية",
                "تم إنشاء النسخة الاحتياطية بنجاح"
            )
        else:
            QMessageBox.warning(
                self,
                "خطأ",
                "فشل في إنشاء النسخة الاحتياطية"
            )
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(
            self,
            "حول البرنامج",
            """
            <h3>نظام إدارة الشحنات المتكامل</h3>
            <p><b>الإصدار:</b> 1.0.0</p>
            <p><b>الوصف:</b> نظام شامل لإدارة الشحنات والموردين والأصناف</p>
            <p><b>المطور:</b> فريق ProShipment</p>
            <p><b>حقوق النشر:</b> © 2024 جميع الحقوق محفوظة</p>
            """
        )
    
    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        reply = QMessageBox.question(
            self,
            "تأكيد الخروج",
            "هل تريد إغلاق البرنامج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إغلاق النوافذ المنفصلة
            if self.settings_window:
                self.settings_window.close()
            if self.items_window:
                self.items_window.close()
            if self.suppliers_window:
                self.suppliers_window.close()
            if self.shipments_window:
                self.shipments_window.close()
            
            event.accept()
        else:
            event.ignore()
